/* AutoScroller Pro Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
    width: 380px;
    min-height: 500px;
}

.container {
    padding: 16px;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e9ecef;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.icon {
    font-size: 20px;
}

h1 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.online {
    background-color: #28a745;
    box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}

.status-dot.offline {
    background-color: #dc3545;
    box-shadow: 0 0 4px rgba(220, 53, 69, 0.5);
}

/* Sections */
.controls-section,
.stats-section,
.settings-section,
.shortcuts-section {
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 6px;
}

h3 {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

/* Control Groups */
.control-group {
    margin-bottom: 16px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.button-row {
    display: flex;
    gap: 8px;
    margin-bottom: 6px;
}

.status-text {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-accent {
    background-color: #17a2b8;
    color: white;
}

.btn-accent:hover {
    background-color: #117a8b;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Statistics */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

/* Settings */
.setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    font-size: 12px;
    color: #495057;
    font-weight: 500;
    min-width: 120px;
}

.setting-item input[type="range"] {
    flex: 1;
    margin: 0 8px;
}

.setting-item span {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    min-width: 40px;
    text-align: right;
}

/* Keyboard Shortcuts */
.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px 0;
}

.shortcut-item:last-child {
    margin-bottom: 0;
}

.shortcut-key {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    color: #495057;
    font-weight: 500;
}

.shortcut-desc {
    font-size: 12px;
    color: #6c757d;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
}

.footer p {
    font-size: 11px;
    color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    body {
        width: 320px;
    }
    
    .container {
        padding: 12px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
