/**
 * AutoScroller Pro - Background Script (Service Worker)
 * Handles keyboard shortcuts, extension lifecycle, and cross-tab coordination
 */

class BackgroundController {
    constructor() {
        this.activeTabStates = new Map(); // Track state per tab
        this.init();
    }
    
    /**
     * Initialize background script
     */
    init() {
        this.setupCommandListeners();
        this.setupTabListeners();
        this.setupInstallListener();
        this.setupMessageListener();
        
        console.log('AutoScroller Pro background script initialized');
    }
    
    /**
     * Setup keyboard command listeners
     */
    setupCommandListeners() {
        chrome.commands.onCommand.addListener(async (command) => {
            try {
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                const activeTab = tabs[0];
                
                if (!activeTab) {
                    console.warn('No active tab found');
                    return;
                }
                
                switch (command) {
                    case 'toggle-autoscroll':
                        await this.toggleAutoScroll(activeTab.id);
                        break;
                        
                    case 'click-load-more':
                        await this.clickLoadMoreOnce(activeTab.id);
                        break;
                        
                    default:
                        console.warn('Unknown command:', command);
                }
                
            } catch (error) {
                console.error('Command handling error:', error);
            }
        });
    }
    
    /**
     * Setup tab event listeners
     */
    setupTabListeners() {
        // Clean up state when tab is closed
        chrome.tabs.onRemoved.addListener((tabId) => {
            this.activeTabStates.delete(tabId);
        });
        
        // Reset state when tab is updated (navigation)
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'loading') {
                this.activeTabStates.delete(tabId);
            }
        });
    }
    
    /**
     * Setup extension install/update listener
     */
    setupInstallListener() {
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('AutoScroller Pro installed');
                this.showWelcomeNotification();
            } else if (details.reason === 'update') {
                console.log('AutoScroller Pro updated to version', chrome.runtime.getManifest().version);
            }
        });
    }
    
    /**
     * Setup message listener for popup communication
     */
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open
        });
    }
    
    /**
     * Handle messages from popup or content scripts
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'getTabState':
                    const tabId = message.tabId || sender.tab?.id;
                    const state = this.activeTabStates.get(tabId) || {};
                    sendResponse({ success: true, state });
                    break;
                    
                case 'setTabState':
                    const targetTabId = message.tabId || sender.tab?.id;
                    this.activeTabStates.set(targetTabId, message.state);
                    sendResponse({ success: true });
                    break;
                    
                case 'clearTabState':
                    const clearTabId = message.tabId || sender.tab?.id;
                    this.activeTabStates.delete(clearTabId);
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Background message handling error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    /**
     * Toggle auto-scroll on active tab
     */
    async toggleAutoScroll(tabId) {
        try {
            // Get current status
            const response = await chrome.tabs.sendMessage(tabId, { action: 'getStatus' });
            
            if (response && response.success) {
                const isScrolling = response.status.isScrolling;
                const action = isScrolling ? 'stopScrolling' : 'startScrolling';
                
                await chrome.tabs.sendMessage(tabId, { action });
                
                // Show notification
                this.showNotification(
                    isScrolling ? 'Auto-scroll stopped' : 'Auto-scroll started',
                    isScrolling ? 'Scrolling has been stopped' : 'Page will now scroll automatically'
                );
            }
            
        } catch (error) {
            console.error('Toggle auto-scroll error:', error);
            this.showNotification(
                'Error',
                'Failed to toggle auto-scroll. Make sure the page is loaded.'
            );
        }
    }
    
    /**
     * Click load more button once on active tab
     */
    async clickLoadMoreOnce(tabId) {
        try {
            const response = await chrome.tabs.sendMessage(tabId, { action: 'clickOnce' });
            
            if (response && response.success) {
                if (response.clicked) {
                    this.showNotification(
                        'Button clicked',
                        'Load more button was clicked successfully'
                    );
                } else {
                    this.showNotification(
                        'No button found',
                        'No load more button was found on this page'
                    );
                }
            }
            
        } catch (error) {
            console.error('Click load more error:', error);
            this.showNotification(
                'Error',
                'Failed to click load more button. Make sure the page is loaded.'
            );
        }
    }
    
    /**
     * Show welcome notification on install
     */
    showWelcomeNotification() {
        this.showNotification(
            'AutoScroller Pro installed!',
            'Use Ctrl+Shift+S to toggle auto-scroll, or click the extension icon.',
            'welcome'
        );
    }
    
    /**
     * Show notification to user
     */
    showNotification(title, message, type = 'basic') {
        try {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: title,
                message: message
            });
        } catch (error) {
            console.error('Notification error:', error);
        }
    }
    
    /**
     * Get extension statistics across all tabs
     */
    getGlobalStats() {
        const stats = {
            activeTabs: this.activeTabStates.size,
            totalScrolls: 0,
            totalClicks: 0
        };
        
        for (const [tabId, state] of this.activeTabStates) {
            if (state.stats) {
                stats.totalScrolls += state.stats.scrollCount || 0;
                stats.totalClicks += state.stats.buttonClickCount || 0;
            }
        }
        
        return stats;
    }
    
    /**
     * Cleanup resources
     */
    cleanup() {
        this.activeTabStates.clear();
        console.log('Background script cleaned up');
    }
}

// Initialize background controller
const backgroundController = new BackgroundController();

// Handle service worker lifecycle
self.addEventListener('activate', () => {
    console.log('AutoScroller Pro service worker activated');
});

self.addEventListener('install', () => {
    console.log('AutoScroller Pro service worker installed');
});

// Cleanup on unload (though service workers don't really "unload")
self.addEventListener('beforeunload', () => {
    backgroundController.cleanup();
});
