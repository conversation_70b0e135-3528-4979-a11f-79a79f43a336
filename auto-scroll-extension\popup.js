/**
 * AutoScroller Pro - Popup Script
 * Handles UI interactions and communication with content script
 */

class PopupController {
    constructor() {
        this.currentTab = null;
        this.isConnected = false;
        this.statusUpdateInterval = null;
        
        this.init();
    }
    
    /**
     * Initialize the popup
     */
    async init() {
        try {
            // Get current active tab
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tabs[0];
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Setup settings
            this.setupSettings();
            
            // Start status updates
            this.startStatusUpdates();
            
            // Initial status check
            this.updateStatus();
            
        } catch (error) {
            console.error('Popup initialization error:', error);
            this.showError('Failed to initialize popup');
        }
    }
    
    /**
     * Setup event listeners for UI elements
     */
    setupEventListeners() {
        // Scroll controls
        document.getElementById('startScrollBtn').addEventListener('click', () => {
            this.sendMessage({ action: 'startScrolling' });
        });
        
        document.getElementById('stopScrollBtn').addEventListener('click', () => {
            this.sendMessage({ action: 'stopScrolling' });
        });
        
        // Button click controls
        document.getElementById('startClickBtn').addEventListener('click', () => {
            this.sendMessage({ action: 'startButtonClicking' });
        });
        
        document.getElementById('stopClickBtn').addEventListener('click', () => {
            this.sendMessage({ action: 'stopButtonClicking' });
        });
        
        document.getElementById('clickOnceBtn').addEventListener('click', () => {
            this.sendMessage({ action: 'clickOnce' });
        });
        
        // Quick actions
        document.getElementById('stopAllBtn').addEventListener('click', () => {
            this.sendMessage({ action: 'stopScrolling' });
            this.sendMessage({ action: 'stopButtonClicking' });
        });
        
        document.getElementById('resetStatsBtn').addEventListener('click', () => {
            this.sendMessage({ action: 'resetStats' });
        });
    }
    
    /**
     * Setup settings controls
     */
    setupSettings() {
        // Scroll delay
        const scrollDelaySlider = document.getElementById('scrollDelay');
        const scrollDelayValue = document.getElementById('scrollDelayValue');
        
        scrollDelaySlider.addEventListener('input', (e) => {
            const value = e.target.value;
            scrollDelayValue.textContent = value;
            this.updateConfig({ scrollDelay: parseInt(value) });
        });
        
        // Scroll amount
        const scrollAmountSlider = document.getElementById('scrollAmount');
        const scrollAmountValue = document.getElementById('scrollAmountValue');
        
        scrollAmountSlider.addEventListener('input', (e) => {
            const value = e.target.value;
            scrollAmountValue.textContent = value;
            this.updateConfig({ scrollAmount: parseInt(value) });
        });
        
        // Button delay
        const buttonDelaySlider = document.getElementById('buttonDelay');
        const buttonDelayValue = document.getElementById('buttonDelayValue');
        
        buttonDelaySlider.addEventListener('input', (e) => {
            const value = e.target.value;
            buttonDelayValue.textContent = value;
            this.updateConfig({ buttonClickDelay: parseInt(value) });
        });
    }
    
    /**
     * Send message to content script
     */
    async sendMessage(message) {
        try {
            if (!this.currentTab) {
                throw new Error('No active tab');
            }
            
            const response = await chrome.tabs.sendMessage(this.currentTab.id, message);
            
            if (response && response.success) {
                this.updateStatus();
                return response;
            } else {
                throw new Error(response?.error || 'Unknown error');
            }
            
        } catch (error) {
            console.error('Message send error:', error);
            this.showError(`Failed to communicate with page: ${error.message}`);
            this.setConnectionStatus(false);
        }
    }
    
    /**
     * Update configuration
     */
    async updateConfig(config) {
        await this.sendMessage({ action: 'updateConfig', config });
    }
    
    /**
     * Start periodic status updates
     */
    startStatusUpdates() {
        this.statusUpdateInterval = setInterval(() => {
            this.updateStatus();
        }, 2000);
    }
    
    /**
     * Update status and statistics
     */
    async updateStatus() {
        try {
            const response = await this.sendMessage({ action: 'getStatus' });
            
            if (response && response.success) {
                this.setConnectionStatus(true);
                this.updateUI(response.status, response.stats, response.config);
            }
            
        } catch (error) {
            this.setConnectionStatus(false);
        }
    }
    
    /**
     * Update UI elements
     */
    updateUI(status, stats, config) {
        // Update status indicators
        document.getElementById('scrollStatus').textContent = 
            status.isScrolling ? 'Running' : 'Stopped';
        document.getElementById('clickStatus').textContent = 
            status.isClickingButtons ? 'Running' : 'Stopped';
        
        // Update statistics
        if (stats) {
            document.getElementById('scrollCount').textContent = stats.scrollCount || 0;
            document.getElementById('clickCount').textContent = stats.buttonClickCount || 0;
            document.getElementById('runtime').textContent = `${stats.runtime || 0}s`;
        }
        
        // Update settings sliders
        if (config) {
            const scrollDelaySlider = document.getElementById('scrollDelay');
            const scrollAmountSlider = document.getElementById('scrollAmount');
            const buttonDelaySlider = document.getElementById('buttonDelay');
            
            if (scrollDelaySlider.value != config.scrollDelay) {
                scrollDelaySlider.value = config.scrollDelay;
                document.getElementById('scrollDelayValue').textContent = config.scrollDelay;
            }
            
            if (scrollAmountSlider.value != config.scrollAmount) {
                scrollAmountSlider.value = config.scrollAmount;
                document.getElementById('scrollAmountValue').textContent = config.scrollAmount;
            }
            
            if (buttonDelaySlider.value != config.buttonClickDelay) {
                buttonDelaySlider.value = config.buttonClickDelay;
                document.getElementById('buttonDelayValue').textContent = config.buttonClickDelay;
            }
        }
        
        // Update button states
        this.updateButtonStates(status);
    }
    
    /**
     * Update button enabled/disabled states
     */
    updateButtonStates(status) {
        const startScrollBtn = document.getElementById('startScrollBtn');
        const stopScrollBtn = document.getElementById('stopScrollBtn');
        const startClickBtn = document.getElementById('startClickBtn');
        const stopClickBtn = document.getElementById('stopClickBtn');
        
        startScrollBtn.disabled = status.isScrolling;
        stopScrollBtn.disabled = !status.isScrolling;
        startClickBtn.disabled = status.isClickingButtons;
        stopClickBtn.disabled = !status.isClickingButtons;
    }
    
    /**
     * Set connection status
     */
    setConnectionStatus(connected) {
        this.isConnected = connected;
        const statusDot = document.getElementById('connectionStatus');
        const statusText = document.getElementById('connectionText');
        
        if (connected) {
            statusDot.className = 'status-dot online';
            statusText.textContent = 'Connected';
        } else {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'Disconnected';
        }
    }
    
    /**
     * Show error message
     */
    showError(message) {
        // You could implement a toast notification here
        console.error('Popup error:', message);
    }
    
    /**
     * Cleanup when popup closes
     */
    cleanup() {
        if (this.statusUpdateInterval) {
            clearInterval(this.statusUpdateInterval);
        }
    }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.popupController = new PopupController();
});

// Cleanup on unload
window.addEventListener('beforeunload', () => {
    if (window.popupController) {
        window.popupController.cleanup();
    }
});
