# 🚀 AutoScroller Pro v3.0.0

**Professional-grade auto-scrolling and image loading extension for Chrome**

## ✨ Features

### 🎯 **Core Functionality**
- **Auto Scroll** - Intelligent scrolling with customizable speed and amount
- **Load More Detection** - Automatically finds and clicks "Load More" buttons
- **Button Selector** - Visual button selection with persistent storage
- **Button Press Automation** - Automated clicking of selected buttons
- **Image Force Loading** - Triggers lazy-loaded images to load immediately

### 🛡️ **Professional Quality**
- **Bulletproof Error Handling** - Graceful failure recovery
- **Real-time Status Updates** - Live connection monitoring
- **Persistent Settings** - Settings saved across sessions
- **Multi-language Support** - Detects buttons in multiple languages
- **Production-ready Code** - Clean, maintainable, documented codebase

### 🌐 **Universal Compatibility**
- Works on **ALL websites**
- Supports modern web frameworks (React, Vue, Angular)
- Handles dynamic content loading
- Compatible with lazy-loading libraries

## 🚀 Installation

### Method 1: Developer Mode (Recommended)
1. Download or clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" (toggle in top-right)
4. Click "Load unpacked" and select the `auto-scroll-extension` folder
5. The extension icon will appear in your toolbar

### Method 2: Chrome Web Store
*Coming soon - pending review*

## 🎮 Usage Guide

### 🔧 **Getting Started**
1. Click the AutoScroller Pro icon in your Chrome toolbar
2. The popup will show connection status (green dot = connected)
3. Choose your desired functionality from the clean interface

### 📜 **Auto Scroll**
- **Start**: Begins continuous scrolling
- **Stop**: Stops scrolling immediately
- **Settings**: Adjust speed (50-2000ms) and amount (100-1000px)

### 🔴 **Load More**
- Automatically detects and clicks buttons containing:
  - "Load More", "Show More", "Next", "Continue"
  - "加载更多", "显示更多", "更多" (Chinese)
  - "Voir plus", "Mehr laden" (French/German)
- **Smart Detection**: Uses text content, ARIA labels, and CSS classes
- **Configurable Delay**: Prevent server overload with adjustable timing

### 🎯 **Button Control**
1. **Select Button**: Click to enter selection mode
2. **Click Target**: Click any button on the webpage
3. **Start Press**: Automatically clicks the selected button
4. **Persistent Storage**: Selected buttons are remembered

### 🖼️ **Image Loading**
- **Force Load All Images**: Triggers lazy-loaded images
- **Automatic Detection**: Finds `data-src`, `data-original`, `data-lazy-src`
- **Event Triggering**: Dispatches scroll/resize events for lazy loaders

## ⚙️ Configuration

### 🎛️ **Settings Panel**
- **Scroll Speed**: Time between scroll actions (50-2000ms)
- **Scroll Amount**: Pixels scrolled per action (100-1000px)
- **Click Delay**: Time between button clicks (100-5000ms)

### 💾 **Data Storage**
- Settings automatically saved to Chrome storage
- Selected buttons persist across browser sessions
- No external servers - all data stays local

## 📊 **Statistics & Monitoring**

### 📈 **Real-time Stats**
- **Scrolls**: Total scroll actions performed
- **Clicks**: Total buttons clicked
- **Images**: Total images force-loaded
- **Runtime**: Session duration

### 🔍 **Connection Status**
- **Green Dot**: Connected and ready
- **Red Dot**: Connection failed - refresh page needed
- **Live Updates**: Status refreshed every 2 seconds

## 🛠️ **Technical Details**

### 🏗️ **Architecture**
- **Manifest V3**: Latest Chrome extension standard
- **Service Worker**: Efficient background processing
- **Content Script**: Isolated page interaction
- **Modern JavaScript**: ES6+ with async/await

### 🔒 **Permissions**
- `activeTab`: Access current tab only
- `storage`: Save settings locally
- `scripting`: Inject content scripts
- `<all_urls>`: Work on all websites

### 🎨 **Code Quality**
- **TypeScript-style JSDoc**: Full documentation
- **Error Boundaries**: Comprehensive error handling
- **Memory Management**: Proper cleanup and disposal
- **Performance Optimized**: Minimal resource usage

## 🐛 **Troubleshooting**

### ❌ **Common Issues**

**"Connection Failed" Error:**
- Refresh the webpage (F5)
- Reload the extension in `chrome://extensions/`
- Check browser console for errors

**Button Selection Not Working:**
- Ensure you're clicking on actual buttons/links
- Try different button types (button, a, input)
- Check if button is visible and clickable

**Load More Not Finding Buttons:**
- Buttons must contain relevant text ("load", "more", "next")
- Check browser console for detection logs
- Try adjusting click delay settings

### 🔧 **Debug Mode**
1. Open browser console (F12)
2. Look for AutoScroller Pro logs
3. Check for error messages
4. Report issues with console output

## 🚀 **Performance**

### ⚡ **Optimizations**
- **Lazy Initialization**: Only loads when needed
- **Event Delegation**: Efficient event handling
- **Memory Cleanup**: Proper resource disposal
- **Throttled Updates**: Prevents UI flooding

### 📊 **Resource Usage**
- **CPU**: Minimal impact during idle
- **Memory**: <5MB typical usage
- **Network**: No external requests
- **Battery**: Negligible drain

## 🔄 **Version History**

### v3.0.0 (Current)
- Complete rewrite with professional architecture
- Bulletproof error handling and recovery
- Modern UI with real-time status updates
- Multi-language button detection
- Production-ready code quality

### v2.x (Legacy)
- Basic functionality with stability issues
- Limited error handling
- Simple UI

## 🤝 **Contributing**

### 🛠️ **Development Setup**
1. Clone the repository
2. Make your changes
3. Test thoroughly on multiple websites
4. Submit pull request with detailed description

### 📝 **Code Standards**
- Use JSDoc for all functions
- Implement proper error handling
- Follow existing code style
- Add comprehensive comments

## 📄 **License**

MIT License - See LICENSE file for details

## 🆘 **Support**

### 📧 **Contact**
- GitHub Issues: Report bugs and feature requests
- Email: [Your email for support]

### 📚 **Resources**
- Chrome Extension Documentation
- Manifest V3 Migration Guide
- JavaScript Best Practices

---

**🚀 AutoScroller Pro - Professional automation for the modern web**

*Built with ❤️ for productivity and efficiency*