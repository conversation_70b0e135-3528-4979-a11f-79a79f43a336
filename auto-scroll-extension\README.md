# 🚀 AutoScroller Pro v3.0.0

**Professional-grade auto-scrolling and "Load More" button automation for Chrome**

## ✨ Features

### 🎯 **Core Functionality**
- **Auto Scroll** - Intelligent scrolling with customizable speed and timing
- **Load More Detection** - Automatically finds and clicks "Load More" buttons in multiple languages
- **Dynamic Content Handling** - Waits for new content to load after button clicks
- **Multi-language Support** - Supports English and German button text variations
- **Keyboard Shortcuts** - Quick access via Ctrl+Shift+S and Ctrl+Shift+L
- **Safety Measures** - Prevents infinite loops and includes emergency stop functionality

### 🛡️ **Professional Quality**
- **Comprehensive Error Handling** - Graceful failure recovery and logging
- **Real-time Status Updates** - Live connection monitoring and statistics
- **Persistent Settings** - Configuration saved across browser sessions
- **Cross-site Compatibility** - Works reliably across different websites
- **Production-ready Code** - Clean, maintainable, well-documented codebase

### 🌐 **Universal Compatibility**
- Works on **ALL websites** including social media, news sites, and content platforms
- Supports modern web frameworks (React, Vue, Angular, etc.)
- Handles dynamic content loading and lazy-loaded images
- Compatible with various button implementations and CSS frameworks

## 🚀 Installation

### Method 1: Developer Mode (Recommended)
1. Download or clone this repository to your computer
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" using the toggle in the top-right corner
4. Click "Load unpacked" and select the `auto-scroll-extension` folder
5. The AutoScroller Pro icon will appear in your Chrome toolbar

### Method 2: Chrome Web Store
*Coming soon - pending review*

### 📋 Prerequisites
- Google Chrome browser (version 88 or later)
- No additional software required

## 🎮 Usage Guide

### 🔧 **Getting Started**
1. Click the AutoScroller Pro icon in your Chrome toolbar
2. The popup will show connection status (green dot = connected)
3. Choose your desired functionality from the intuitive interface

### 📜 **Auto Scroll**
- **Start Scroll**: Begins continuous automatic scrolling
- **Stop Scroll**: Stops scrolling immediately
- **Settings**: Adjust scroll speed (100-5000ms) and scroll amount (100-1000px)
- **Safety**: Automatically stops after maximum attempts or time limits

### 🔴 **Load More Buttons**
- **Auto Click**: Continuously finds and clicks "Load More" buttons
- **Click Once**: Manually trigger a single button click
- **Smart Detection**: Detects buttons with text patterns including:
  - English: "Load More", "Show More", "See More", "View More", "Read More", "Continue", "Next"
  - German: "Mehr laden", "Mehr anzeigen", "Mehr sehen", "Weiter laden", "Fortsetzen", "Erweitern"
- **Multiple Detection Methods**: Uses text content, ARIA labels, CSS classes, and element attributes
- **Dynamic Content Handling**: Waits for new content to load after clicking buttons

### ⚙️ **Settings & Configuration**
- **Scroll Delay**: Time between scroll actions (100-5000ms)
- **Scroll Amount**: Pixels scrolled per action (100-1000px)
- **Button Click Delay**: Time between button clicks (500-10000ms)
- **Auto-save**: All settings are automatically saved and restored

### ⌨️ **Keyboard Shortcuts**
- **Ctrl+Shift+S** (Cmd+Shift+S on Mac): Toggle auto-scrolling on/off
- **Ctrl+Shift+L** (Cmd+Shift+L on Mac): Click "Load More" button once

## ⚙️ Configuration

### 🎛️ **Settings Panel**
- **Scroll Speed**: Time between scroll actions (50-2000ms)
- **Scroll Amount**: Pixels scrolled per action (100-1000px)
- **Click Delay**: Time between button clicks (100-5000ms)

### 💾 **Data Storage**
- Settings automatically saved to Chrome storage
- Selected buttons persist across browser sessions
- No external servers - all data stays local

## 📊 **Statistics & Monitoring**

### 📈 **Real-time Stats**
- **Scrolls**: Total scroll actions performed
- **Clicks**: Total buttons clicked
- **Images**: Total images force-loaded
- **Runtime**: Session duration

### 🔍 **Connection Status**
- **Green Dot**: Connected and ready
- **Red Dot**: Connection failed - refresh page needed
- **Live Updates**: Status refreshed every 2 seconds

## 🛠️ **Technical Details**

### 🏗️ **Architecture**
- **Manifest V3**: Latest Chrome extension standard
- **Service Worker**: Efficient background processing
- **Content Script**: Isolated page interaction
- **Modern JavaScript**: ES6+ with async/await

### 🔒 **Permissions**
- `activeTab`: Access current tab only
- `storage`: Save settings locally
- `scripting`: Inject content scripts
- `<all_urls>`: Work on all websites

### 🎨 **Code Quality**
- **TypeScript-style JSDoc**: Full documentation
- **Error Boundaries**: Comprehensive error handling
- **Memory Management**: Proper cleanup and disposal
- **Performance Optimized**: Minimal resource usage

## 🧪 **Testing**

### 📄 **Test Page**
Open `test-page.html` in your browser to test all extension features:
- Multiple "Load More" button variations in English and German
- Dynamic content loading simulation
- Various button types and implementations
- Scroll behavior testing
- Real-time statistics and monitoring

### 🔍 **Testing Checklist**
- [ ] Extension loads without errors
- [ ] Popup shows green connection status
- [ ] Auto-scroll starts and stops correctly
- [ ] "Load More" buttons are detected and clicked
- [ ] Settings are saved and restored
- [ ] Keyboard shortcuts work
- [ ] Extension works on different websites
- [ ] Safety limits prevent infinite loops

## 🐛 **Troubleshooting**

### ❌ **Common Issues**

**"Disconnected" Status in Popup:**
- Refresh the webpage (F5 or Ctrl+R)
- Reload the extension in `chrome://extensions/`
- Check browser console (F12) for error messages
- Ensure the page has finished loading

**Buttons Not Being Detected:**
- Verify buttons contain recognizable text ("Load More", "Show More", etc.)
- Check browser console for detection logs
- Try adjusting the button click delay in settings
- Ensure buttons are visible and not disabled

**Auto-scroll Not Working:**
- Check if the page is scrollable (has content below the fold)
- Verify scroll settings (delay and amount) are reasonable
- Look for JavaScript errors in the browser console
- Try refreshing the page and restarting the extension

**Extension Not Loading:**
- Ensure Chrome is version 88 or later
- Check that all extension files are present
- Verify manifest.json is valid
- Try reloading the extension in developer mode

### 🔧 **Debug Mode**
1. Open browser console (F12 → Console tab)
2. Look for `[AutoScroller Pro]` log messages
3. Check for error messages in red
4. Monitor network requests if buttons aren't working
5. Report issues with console output and steps to reproduce

## 🚀 **Performance**

### ⚡ **Optimizations**
- **Lazy Initialization**: Only loads when needed
- **Event Delegation**: Efficient event handling
- **Memory Cleanup**: Proper resource disposal
- **Throttled Updates**: Prevents UI flooding

### 📊 **Resource Usage**
- **CPU**: Minimal impact during idle
- **Memory**: <5MB typical usage
- **Network**: No external requests
- **Battery**: Negligible drain

## 🔄 **Version History**

### v3.0.0 (Current)
- Complete rewrite with professional architecture
- Bulletproof error handling and recovery
- Modern UI with real-time status updates
- Multi-language button detection
- Production-ready code quality

### v2.x (Legacy)
- Basic functionality with stability issues
- Limited error handling
- Simple UI

## 🤝 **Contributing**

### 🛠️ **Development Setup**
1. Clone the repository
2. Make your changes
3. Test thoroughly on multiple websites
4. Submit pull request with detailed description

### 📝 **Code Standards**
- Use JSDoc for all functions
- Implement proper error handling
- Follow existing code style
- Add comprehensive comments

## 📄 **License**

MIT License - See LICENSE file for details

## 🆘 **Support**

### 📧 **Contact**
- GitHub Issues: Report bugs and feature requests
- Email: [Your email for support]

### 📚 **Resources**
- Chrome Extension Documentation
- Manifest V3 Migration Guide
- JavaScript Best Practices

---

**🚀 AutoScroller Pro - Professional automation for the modern web**

*Built with ❤️ for productivity and efficiency*