# Icons Directory

This directory should contain the extension icons in the following sizes:

- icon16.png (16x16 pixels)
- icon32.png (32x32 pixels) 
- icon48.png (48x48 pixels)
- icon128.png (128x128 pixels)

For now, you can use any PNG images with these dimensions, or create simple colored squares as placeholders.

The icons should represent the AutoScroller Pro extension - perhaps a scroll arrow or automation symbol.

## Creating Icons

You can:
1. Use an online icon generator
2. Create simple colored squares in an image editor
3. Use existing icons from icon libraries (with proper licensing)
4. Design custom icons that represent scrolling/automation

## Temporary Solution

If you don't have icons ready, you can temporarily comment out the icon references in manifest.json and the extension will still work, just without custom icons.
