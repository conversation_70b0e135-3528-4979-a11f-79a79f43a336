<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoScroller Pro</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <span class="icon">🚀</span>
                <h1>AutoScroller Pro</h1>
            </div>
            <div class="status-indicator">
                <span id="connectionStatus" class="status-dot offline"></span>
                <span id="connectionText">Connecting...</span>
            </div>
        </div>

        <!-- Main Controls -->
        <div class="controls-section">
            <h2>Controls</h2>
            
            <!-- Auto Scroll Controls -->
            <div class="control-group">
                <h3>Auto Scroll</h3>
                <div class="button-row">
                    <button id="startScrollBtn" class="btn btn-primary">Start Scroll</button>
                    <button id="stopScrollBtn" class="btn btn-secondary">Stop Scroll</button>
                </div>
                <div class="status-text">
                    <span id="scrollStatus">Stopped</span>
                </div>
            </div>

            <!-- Button Clicking Controls -->
            <div class="control-group">
                <h3>Load More Buttons</h3>
                <div class="button-row">
                    <button id="startClickBtn" class="btn btn-primary">Auto Click</button>
                    <button id="stopClickBtn" class="btn btn-secondary">Stop Click</button>
                </div>
                <div class="button-row">
                    <button id="clickOnceBtn" class="btn btn-accent">Click Once</button>
                </div>
                <div class="status-text">
                    <span id="clickStatus">Stopped</span>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="control-group">
                <h3>Quick Actions</h3>
                <div class="button-row">
                    <button id="stopAllBtn" class="btn btn-danger">Stop All</button>
                    <button id="resetStatsBtn" class="btn btn-secondary">Reset Stats</button>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-section">
            <h2>Statistics</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label">Scrolls:</span>
                    <span id="scrollCount" class="stat-value">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Clicks:</span>
                    <span id="clickCount" class="stat-value">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Runtime:</span>
                    <span id="runtime" class="stat-value">0s</span>
                </div>
            </div>
        </div>

        <!-- Settings -->
        <div class="settings-section">
            <h2>Settings</h2>
            
            <div class="setting-item">
                <label for="scrollDelay">Scroll Delay (ms):</label>
                <input type="range" id="scrollDelay" min="100" max="5000" step="100" value="1000">
                <span id="scrollDelayValue">1000</span>
            </div>
            
            <div class="setting-item">
                <label for="scrollAmount">Scroll Amount (px):</label>
                <input type="range" id="scrollAmount" min="100" max="1000" step="50" value="300">
                <span id="scrollAmountValue">300</span>
            </div>
            
            <div class="setting-item">
                <label for="buttonDelay">Button Click Delay (ms):</label>
                <input type="range" id="buttonDelay" min="500" max="10000" step="500" value="2000">
                <span id="buttonDelayValue">2000</span>
            </div>
        </div>

        <!-- Keyboard Shortcuts -->
        <div class="shortcuts-section">
            <h2>Keyboard Shortcuts</h2>
            <div class="shortcut-item">
                <span class="shortcut-key">Ctrl+Shift+S</span>
                <span class="shortcut-desc">Toggle Auto Scroll</span>
            </div>
            <div class="shortcut-item">
                <span class="shortcut-key">Ctrl+Shift+L</span>
                <span class="shortcut-desc">Click Load More Once</span>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>v3.0.0 | Professional automation for the modern web</p>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
