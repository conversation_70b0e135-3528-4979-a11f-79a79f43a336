/**
 * AutoScroller Pro - Content Script
 * Professional auto-scrolling and button automation
 */

class AutoScrollerPro {
    constructor() {
        this.isScrolling = false;
        this.isClickingButtons = false;
        this.scrollInterval = null;
        this.buttonClickInterval = null;
        this.mutationObserver = null;

        // Configuration with defaults
        this.config = {
            scrollDelay: 1000,        // ms between scrolls
            scrollAmount: 300,        // pixels per scroll
            buttonClickDelay: 2000,   // ms between button clicks
            maxScrollAttempts: 1000,  // prevent infinite scrolling
            maxButtonClicks: 100,     // prevent infinite clicking
            waitForContent: 3000,     // ms to wait for new content
            scrollThreshold: 100,     // pixels from bottom to trigger
            maxSessionTime: 3600000,  // max session time (1 hour)
            emergencyStop: false,     // emergency stop flag
            safetyChecks: true        // enable safety checks
        };

        // Statistics
        this.stats = {
            scrollCount: 0,
            buttonClickCount: 0,
            startTime: null,
            lastContentHeight: 0
        };

        // Button detection patterns (English and German)
        this.buttonPatterns = [
            // English patterns
            /load\s*more/i,
            /show\s*more/i,
            /see\s*more/i,
            /view\s*more/i,
            /read\s*more/i,
            /more\s*posts/i,
            /more\s*content/i,
            /continue/i,
            /next/i,
            /expand/i,

            // German patterns
            /mehr\s*laden/i,
            /mehr\s*anzeigen/i,
            /mehr\s*sehen/i,
            /weiter\s*laden/i,
            /fortsetzen/i,
            /erweitern/i,
            /nächste/i,
            /weitere/i
        ];

        // Common button selectors
        this.buttonSelectors = [
            'button[class*="load"]',
            'button[class*="more"]',
            'button[class*="show"]',
            'button[class*="next"]',
            'a[class*="load"]',
            'a[class*="more"]',
            'a[class*="show"]',
            'a[class*="next"]',
            '[role="button"]',
            '.load-more',
            '.show-more',
            '.btn-load',
            '.btn-more',
            '.pagination-next'
        ];

        this.init();
    }

    /**
     * Initialize the extension
     */
    init() {
        this.loadConfig();
        this.setupMessageListener();
        this.setupMutationObserver();
        this.logInfo('AutoScroller Pro initialized');
    }

    /**
     * Load configuration from storage
     */
    async loadConfig() {
        try {
            const result = await chrome.storage.sync.get(['autoScrollerConfig']);
            if (result.autoScrollerConfig) {
                this.config = { ...this.config, ...result.autoScrollerConfig };
            }
        } catch (error) {
            this.logError('Failed to load config:', error);
        }
    }

    /**
     * Save configuration to storage
     */
    async saveConfig() {
        try {
            await chrome.storage.sync.set({ autoScrollerConfig: this.config });
        } catch (error) {
            this.logError('Failed to save config:', error);
        }
    }

    /**
     * Setup message listener for popup communication
     */
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sendResponse);
            return true; // Keep message channel open for async response
        });
    }

    /**
     * Handle messages from popup and background script
     */
    async handleMessage(message, sendResponse) {
        try {
            switch (message.action) {
                case 'startScrolling':
                    this.startScrolling();
                    sendResponse({ success: true, status: 'scrolling' });
                    break;

                case 'stopScrolling':
                    this.stopScrolling();
                    sendResponse({ success: true, status: 'stopped' });
                    break;

                case 'startButtonClicking':
                    this.startButtonClicking();
                    sendResponse({ success: true, status: 'clicking' });
                    break;

                case 'stopButtonClicking':
                    this.stopButtonClicking();
                    sendResponse({ success: true, status: 'stopped' });
                    break;

                case 'clickOnce':
                    const clicked = await this.clickLoadMoreButton();
                    sendResponse({ success: true, clicked });
                    break;

                case 'getStatus':
                    sendResponse({
                        success: true,
                        status: this.getStatus(),
                        stats: this.getStats(),
                        config: this.config
                    });
                    break;

                case 'updateConfig':
                    this.config = { ...this.config, ...message.config };
                    await this.saveConfig();
                    sendResponse({ success: true });
                    break;

                case 'resetStats':
                    this.resetStats();
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            this.logError('Message handling error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * Start auto-scrolling
     */
    startScrolling() {
        if (this.isScrolling) return;

        this.isScrolling = true;
        this.stats.startTime = Date.now();
        this.stats.scrollCount = 0;
        this.stats.lastContentHeight = document.body.scrollHeight;

        this.logInfo('Starting auto-scroll');

        this.scrollInterval = setInterval(() => {
            this.performScroll();
        }, this.config.scrollDelay);
    }

    /**
     * Stop auto-scrolling
     */
    stopScrolling() {
        if (!this.isScrolling) return;

        this.isScrolling = false;
        if (this.scrollInterval) {
            clearInterval(this.scrollInterval);
            this.scrollInterval = null;
        }

        this.logInfo('Stopped auto-scroll');
    }

    /**
     * Perform a single scroll action
     */
    performScroll() {
        try {
            // Emergency stop check
            if (this.config.emergencyStop) {
                this.logInfo('Emergency stop activated');
                this.stopAll();
                return;
            }

            // Safety check: max attempts
            if (this.stats.scrollCount >= this.config.maxScrollAttempts) {
                this.logInfo('Max scroll attempts reached, stopping');
                this.stopScrolling();
                return;
            }

            // Safety check: max session time
            if (this.stats.startTime && (Date.now() - this.stats.startTime) > this.config.maxSessionTime) {
                this.logInfo('Max session time reached, stopping for safety');
                this.stopAll();
                return;
            }

            // Safety check: page responsiveness
            if (this.config.safetyChecks && !this.checkPageResponsiveness()) {
                this.logInfo('Page appears unresponsive, stopping for safety');
                this.stopAll();
                return;
            }

            // Safety check: stuck state detection
            if (this.checkForStuckState()) {
                this.logInfo('Stuck state detected, stopping');
                this.stopScrolling();
                return;
            }

            // Check if we're near the bottom
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.body.scrollHeight;
            const distanceFromBottom = documentHeight - (scrollTop + windowHeight);

            if (distanceFromBottom <= this.config.scrollThreshold) {
                this.logInfo('Near bottom of page, checking for load more buttons');
                // Try to click load more button before continuing
                this.clickLoadMoreButton().then(clicked => {
                    if (!clicked) {
                        this.logInfo('No load more button found, continuing scroll');
                    }
                });
            }

            // Perform scroll
            window.scrollBy({
                top: this.config.scrollAmount,
                behavior: 'smooth'
            });

            this.stats.scrollCount++;
            this.logDebug(`Scrolled ${this.stats.scrollCount} times`);

        } catch (error) {
            this.logError('Scroll error:', error);
        }
    }

    /**
     * Start automatic button clicking
     */
    startButtonClicking() {
        if (this.isClickingButtons) return;

        this.isClickingButtons = true;
        this.stats.buttonClickCount = 0;

        this.logInfo('Starting button clicking automation');

        this.buttonClickInterval = setInterval(() => {
            this.clickLoadMoreButton();
        }, this.config.buttonClickDelay);
    }

    /**
     * Stop automatic button clicking
     */
    stopButtonClicking() {
        if (!this.isClickingButtons) return;

        this.isClickingButtons = false;
        if (this.buttonClickInterval) {
            clearInterval(this.buttonClickInterval);
            this.buttonClickInterval = null;
        }

        this.logInfo('Stopped button clicking automation');
    }

    /**
     * Find and click a "Load More" button
     * @returns {Promise<boolean>} True if button was found and clicked
     */
    async clickLoadMoreButton() {
        try {
            // Safety check: max clicks
            if (this.stats.buttonClickCount >= this.config.maxButtonClicks) {
                this.logInfo('Max button clicks reached, stopping');
                this.stopButtonClicking();
                return false;
            }

            const button = this.findLoadMoreButton();
            if (!button) {
                this.logDebug('No load more button found');
                return false;
            }

            // Check if button is visible and clickable
            if (!this.isElementClickable(button)) {
                this.logDebug('Button found but not clickable');
                return false;
            }

            this.logInfo('Clicking load more button:', button.textContent.trim());

            // Store current page height to detect new content
            const beforeHeight = document.body.scrollHeight;

            // Click the button
            button.click();
            this.stats.buttonClickCount++;

            // Wait for potential new content
            await this.waitForNewContent(beforeHeight);

            return true;

        } catch (error) {
            this.logError('Button click error:', error);
            return false;
        }
    }

    /**
     * Find a "Load More" button on the page
     * @returns {Element|null} The button element or null if not found
     */
    findLoadMoreButton() {
        // First try CSS selectors
        for (const selector of this.buttonSelectors) {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                if (this.isLoadMoreButton(element)) {
                    return element;
                }
            }
        }

        // Then try all buttons and links
        const allButtons = document.querySelectorAll('button, a, [role="button"], input[type="button"], input[type="submit"]');
        for (const button of allButtons) {
            if (this.isLoadMoreButton(button)) {
                return button;
            }
        }

        return null;
    }

    /**
     * Check if an element is a "Load More" button
     * @param {Element} element - The element to check
     * @returns {boolean} True if it's a load more button
     */
    isLoadMoreButton(element) {
        if (!element) return false;

        // Get text content from element and its children
        const textContent = element.textContent?.toLowerCase().trim() || '';
        const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';
        const title = element.getAttribute('title')?.toLowerCase() || '';
        const className = element.className?.toLowerCase() || '';
        const id = element.id?.toLowerCase() || '';

        // Combine all text sources
        const allText = `${textContent} ${ariaLabel} ${title} ${className} ${id}`;

        // Check against patterns
        for (const pattern of this.buttonPatterns) {
            if (pattern.test(allText)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if an element is clickable
     * @param {Element} element - The element to check
     * @returns {boolean} True if clickable
     */
    isElementClickable(element) {
        if (!element) return false;

        // Check if element is visible
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) return false;

        // Check if element is in viewport or near it
        const windowHeight = window.innerHeight;
        const windowWidth = window.innerWidth;

        if (rect.bottom < -100 || rect.top > windowHeight + 100 ||
            rect.right < -100 || rect.left > windowWidth + 100) {
            return false;
        }

        // Check if element is not disabled
        if (element.disabled || element.getAttribute('aria-disabled') === 'true') {
            return false;
        }

        // Check computed style
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
            return false;
        }

        return true;
    }

    /**
     * Wait for new content to load after button click
     * @param {number} beforeHeight - Page height before click
     * @returns {Promise<boolean>} True if new content was detected
     */
    async waitForNewContent(beforeHeight) {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = this.config.waitForContent / 100; // Check every 100ms

            const checkForNewContent = () => {
                attempts++;
                const currentHeight = document.body.scrollHeight;

                // New content detected
                if (currentHeight > beforeHeight) {
                    this.logDebug('New content detected');
                    resolve(true);
                    return;
                }

                // Timeout reached
                if (attempts >= maxAttempts) {
                    this.logDebug('Timeout waiting for new content');
                    resolve(false);
                    return;
                }

                // Continue checking
                setTimeout(checkForNewContent, 100);
            };

            // Start checking after a short delay
            setTimeout(checkForNewContent, 100);
        });
    }

    /**
     * Setup mutation observer to detect dynamic content changes
     */
    setupMutationObserver() {
        this.mutationObserver = new MutationObserver((mutations) => {
            let contentChanged = false;

            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    contentChanged = true;
                }
            });

            if (contentChanged) {
                this.logDebug('DOM content changed');
                // Update last content height
                this.stats.lastContentHeight = document.body.scrollHeight;
            }
        });

        // Start observing
        this.mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Get current status
     * @returns {Object} Status object
     */
    getStatus() {
        return {
            isScrolling: this.isScrolling,
            isClickingButtons: this.isClickingButtons,
            connected: true
        };
    }

    /**
     * Get current statistics
     * @returns {Object} Stats object
     */
    getStats() {
        const runtime = this.stats.startTime ? Date.now() - this.stats.startTime : 0;
        return {
            ...this.stats,
            runtime: Math.floor(runtime / 1000) // seconds
        };
    }

    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            scrollCount: 0,
            buttonClickCount: 0,
            startTime: null,
            lastContentHeight: document.body.scrollHeight
        };
        this.logInfo('Statistics reset');
    }

    /**
     * Stop all automation
     */
    stopAll() {
        this.stopScrolling();
        this.stopButtonClicking();
        this.logInfo('All automation stopped');
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        this.stopAll();
        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
            this.mutationObserver = null;
        }
        this.logInfo('AutoScroller Pro cleaned up');
    }

    /**
     * Check if page is responsive (safety measure)
     * @returns {boolean} True if page appears responsive
     */
    checkPageResponsiveness() {
        try {
            // Check if document is still accessible
            if (!document || !document.body) {
                return false;
            }

            // Check if we can still interact with the DOM
            const testElement = document.createElement('div');
            document.body.appendChild(testElement);
            document.body.removeChild(testElement);

            return true;
        } catch (error) {
            this.logError('Page responsiveness check failed:', error);
            return false;
        }
    }

    /**
     * Emergency stop all automation
     */
    emergencyStop() {
        this.config.emergencyStop = true;
        this.stopAll();
        this.logInfo('EMERGENCY STOP ACTIVATED');
    }

    /**
     * Reset emergency stop
     */
    resetEmergencyStop() {
        this.config.emergencyStop = false;
        this.logInfo('Emergency stop reset');
    }

    /**
     * Check for potential infinite loops or stuck states
     */
    checkForStuckState() {
        const currentHeight = document.body.scrollHeight;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // If we've been scrolling for a while but height hasn't changed
        if (this.stats.scrollCount > 50 && currentHeight === this.stats.lastContentHeight) {
            this.logInfo('Potential stuck state detected - no new content loaded');
            return true;
        }

        // If we're at the bottom and can't scroll further
        const windowHeight = window.innerHeight;
        const distanceFromBottom = currentHeight - (scrollTop + windowHeight);
        if (distanceFromBottom <= 0 && this.stats.scrollCount > 10) {
            this.logInfo('Reached bottom of page');
            return true;
        }

        return false;
    }

    /**
     * Validate configuration values
     */
    validateConfig() {
        const config = this.config;

        // Ensure reasonable limits
        config.scrollDelay = Math.max(50, Math.min(10000, config.scrollDelay));
        config.scrollAmount = Math.max(50, Math.min(2000, config.scrollAmount));
        config.buttonClickDelay = Math.max(500, Math.min(30000, config.buttonClickDelay));
        config.maxScrollAttempts = Math.max(10, Math.min(10000, config.maxScrollAttempts));
        config.maxButtonClicks = Math.max(1, Math.min(1000, config.maxButtonClicks));
        config.waitForContent = Math.max(1000, Math.min(30000, config.waitForContent));

        this.logDebug('Configuration validated');
    }

    /**
     * Logging methods
     */
    logInfo(message, ...args) {
        console.log(`[AutoScroller Pro] ${message}`, ...args);
    }

    logDebug(message, ...args) {
        console.debug(`[AutoScroller Pro] ${message}`, ...args);
    }

    logError(message, ...args) {
        console.error(`[AutoScroller Pro] ${message}`, ...args);
    }
}

// Initialize the extension when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.autoScrollerPro = new AutoScrollerPro();
    });
} else {
    window.autoScrollerPro = new AutoScrollerPro();
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.autoScrollerPro) {
        window.autoScrollerPro.cleanup();
    }
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (event) => {
    // Ctrl+Shift+S (or Cmd+Shift+S on Mac) - Toggle scrolling
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        if (window.autoScrollerPro) {
            if (window.autoScrollerPro.isScrolling) {
                window.autoScrollerPro.stopScrolling();
            } else {
                window.autoScrollerPro.startScrolling();
            }
        }
    }

    // Ctrl+Shift+L (or Cmd+Shift+L on Mac) - Click load more once
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'L') {
        event.preventDefault();
        if (window.autoScrollerPro) {
            window.autoScrollerPro.clickLoadMoreButton();
        }
    }
});