# 📦 AutoScroller Pro Installation Guide

## 🚀 Quick Installation (5 minutes)

### Step 1: Download the Extension
1. Download this repository as a ZIP file or clone it:
   ```bash
   git clone [repository-url]
   ```
2. Extract the ZIP file to a folder on your computer
3. Navigate to the `auto-scroll-extension` folder

### Step 2: Install in Chrome
1. Open Google Chrome
2. Type `chrome://extensions/` in the address bar and press Enter
3. Enable "Developer mode" by clicking the toggle in the top-right corner
4. Click the "Load unpacked" button
5. Select the `auto-scroll-extension` folder
6. The extension should now appear in your extensions list

### Step 3: Verify Installation
1. Look for the AutoScroller Pro icon in your Chrome toolbar
2. Click the icon to open the popup
3. You should see a green "Connected" status
4. Test on the included `test-page.html` file

## 🔧 Detailed Setup Instructions

### Prerequisites
- **Google Chrome** version 88 or later
- **Operating System**: Windows, macOS, or Linux
- **Permissions**: Ability to install Chrome extensions in developer mode

### File Structure Verification
Ensure your `auto-scroll-extension` folder contains:
```
auto-scroll-extension/
├── manifest.json          # Extension configuration
├── content.js            # Main automation logic
├── popup.html            # User interface
├── popup.js              # UI functionality
├── popup.css             # UI styling
├── background.js         # Background coordination
├── test-page.html        # Testing page
├── README.md             # Documentation
├── INSTALLATION.md       # This file
└── icons/                # Extension icons (optional)
    └── README.md
```

### Icon Setup (Optional)
The extension references icon files in the `icons/` directory. You can:
1. **Option A**: Add your own PNG icons (16x16, 32x32, 48x48, 128x128 pixels)
2. **Option B**: Comment out icon references in `manifest.json` lines 18-25 and 47-52
3. **Option C**: Use the extension without custom icons (Chrome will use defaults)

### Permissions Explanation
The extension requests these permissions:
- **activeTab**: Access to the current tab for automation
- **storage**: Save your settings and preferences
- **scripting**: Inject automation scripts into web pages
- **host_permissions**: Work on all websites

## 🧪 Testing Your Installation

### Basic Functionality Test
1. Open the included `test-page.html` in Chrome
2. Click the AutoScroller Pro icon in your toolbar
3. Verify the popup shows "Connected" status
4. Try these features:
   - Click "Start Scroll" and watch the page scroll automatically
   - Click "Auto Click" to automatically click "Load More" buttons
   - Test keyboard shortcuts: Ctrl+Shift+S to toggle scrolling

### Real Website Testing
1. Visit a website with "Load More" buttons (e.g., social media, news sites)
2. Open the extension popup
3. Click "Auto Click" to automatically find and click load more buttons
4. Use "Start Scroll" for continuous scrolling

## 🔄 Updating the Extension

### Manual Update
1. Download the latest version
2. Replace the old `auto-scroll-extension` folder
3. Go to `chrome://extensions/`
4. Click the refresh icon on the AutoScroller Pro extension
5. Your settings will be preserved

### Automatic Updates
When published to the Chrome Web Store, updates will be automatic.

## 🗑️ Uninstalling

### Complete Removal
1. Go to `chrome://extensions/`
2. Find AutoScroller Pro in the list
3. Click "Remove"
4. Confirm the removal
5. Delete the extension folder from your computer

### Temporary Disable
1. Go to `chrome://extensions/`
2. Toggle off the AutoScroller Pro extension
3. Re-enable when needed

## 🆘 Installation Troubleshooting

### "Failed to load extension" Error
- **Cause**: Missing or corrupted files
- **Solution**: Re-download the extension and verify all files are present

### "Manifest file is missing or unreadable" Error
- **Cause**: Invalid manifest.json file
- **Solution**: Ensure manifest.json is valid JSON and not corrupted

### Extension Icon Not Appearing
- **Cause**: Chrome toolbar is full or extension is disabled
- **Solution**: Check `chrome://extensions/` and ensure the extension is enabled

### "This extension may have been corrupted" Error
- **Cause**: Files were modified or corrupted
- **Solution**: Re-download and reinstall the extension

### Permission Denied Errors
- **Cause**: Chrome security settings or corporate policies
- **Solution**: Check with your system administrator or try on a personal device

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section in README.md
2. Open browser console (F12) and look for error messages
3. Test on the included test-page.html
4. Verify your Chrome version is 88 or later
5. Try disabling other extensions temporarily

## 🎉 Success!

Once installed successfully, you should see:
- ✅ AutoScroller Pro icon in Chrome toolbar
- ✅ Green "Connected" status in popup
- ✅ Working auto-scroll and button detection
- ✅ Keyboard shortcuts responding
- ✅ Settings saving and loading properly

Enjoy automated scrolling and button clicking across the web!
