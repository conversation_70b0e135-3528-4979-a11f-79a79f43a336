# Booru Aggregator Scraper

Die<PERSON> Sc<PERSON>t scraped Bilder von <PERSON>u-Aggregator-Website https://booru.cocomi.eu.org/

## Features

- Scraped mehrere Seiten automatisch
- Lädt Bilder mit sinnvollen Dateinamen herunter
- Speichert Metadaten zu jedem Bild
- Vermeidet doppelte Downloads
- Concurrent Downloads für bessere Performance
- Respektiert Server-Limits mit Pausen

## Installation

```bash
pip install -r requirements.txt
```

## Verwendung

### Einfache Version
```bash
python booru_scraper.py
```

### Erweiterte Version (empfohlen)
```bash
python advanced_booru_scraper.py
```

## Konfiguration

Du kannst die Scraper-Parameter anpassen:

```python
scraper = AdvancedBooruScraper(
    base_url="https://booru.cocomi.eu.org/",
    download_dir="meine_bilder",
    max_workers=3  # An<PERSON>hl gleichzeitiger Downloads
)

# Scrape Seiten 1-10
images, results = scraper.scrape_pages(start_page=1, max_pages=10)
```

## Output

- Bilder werden in `downloaded_images/` gespeichert
- Metadaten werden als `.json` Dateien neben den Bildern gespeichert
- `scraping_summary.json` enthält eine Zusammenfassung
- `page_X_data.json` enthält Daten für jede Seite

## Hinweise

- Das Script ist respektvoll und macht Pausen zwischen Requests
- Bereits heruntergeladene Bilder werden übersprungen
- Bei Fehlern wird weitergemacht und am Ende eine Zusammenfassung gezeigt

## API Check

Falls die Website eine API hat, könntest du auch direkt die API verwenden. Schaue in den Browser-Entwicklertools nach API-Calls oder prüfe ob es eine `/api/` Route gibt.