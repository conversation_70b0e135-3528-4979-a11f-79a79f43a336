<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoScroller Pro Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .button {
            padding: 12px 24px;
            margin: 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.2s;
        }
        
        .load-more {
            background: #4CAF50;
            color: white;
        }
        
        .download-btn {
            background: #2196F3;
            color: white;
        }
        
        .test-btn {
            background: #FF9800;
            color: white;
        }
        
        .button:hover {
            opacity: 0.8;
            transform: translateY(-2px);
        }
        
        .content-block {
            height: 300px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
            border-radius: 8px;
        }
        
        .long-content {
            height: 1000px;
            background: repeating-linear-gradient(
                45deg,
                #f0f0f0,
                #f0f0f0 50px,
                #e0e0e0 50px,
                #e0e0e0 100px
            );
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #333;
            border-radius: 8px;
        }
        
        .stats {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        
        .checkbox-group {
            margin: 15px 0;
        }
        
        .checkbox-group label {
            display: block;
            margin: 8px 0;
            cursor: pointer;
        }
        
        .checkbox-group input {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 AutoScroller Pro Test Page</h1>
        <p>Test all extension features on this page</p>
    </div>
    
    <div class="section">
        <h2>📜 Auto Scroll Test</h2>
        <p>Use the extension to test automatic scrolling on this long page.</p>
        <div class="content-block">Short content block for scrolling</div>
    </div>
    
    <div class="section">
        <h2>🔴 Load More Test</h2>
        <p>Test the Load More button detection:</p>

        <!-- English buttons -->
        <button class="button load-more" onclick="loadMore()">Load More</button>
        <button class="button load-more" onclick="loadMore()">Show More</button>
        <button class="button load-more" onclick="loadMore()">See More</button>
        <button class="button load-more" onclick="loadMore()">View More</button>
        <button class="button load-more" onclick="loadMore()">Read More</button>
        <button class="button load-more" onclick="loadMore()">More Posts</button>
        <button class="button load-more" onclick="loadMore()">Continue</button>
        <a href="#" class="button load-more" onclick="loadMore(); return false;">Next</a>

        <!-- German buttons -->
        <button class="button load-more" onclick="loadMore()">Mehr laden</button>
        <button class="button load-more" onclick="loadMore()">Mehr anzeigen</button>
        <button class="button load-more" onclick="loadMore()">Mehr sehen</button>
        <button class="button load-more" onclick="loadMore()">Weiter laden</button>
        <button class="button load-more" onclick="loadMore()">Fortsetzen</button>
        <button class="button load-more" onclick="loadMore()">Erweitern</button>
        <button class="button load-more" onclick="loadMore()">Nächste</button>
        <button class="button load-more" onclick="loadMore()">Weitere</button>

        <!-- Buttons with ARIA labels -->
        <button class="button load-more" aria-label="Load more content" onclick="loadMore()">⬇️</button>
        <button class="button load-more" title="Show more items" onclick="loadMore()">+</button>

        <!-- CSS class based buttons -->
        <div class="load-more button" onclick="loadMore()">Load More (div)</div>
        <span class="show-more button" onclick="loadMore()">Show More (span)</span>

        <div class="stats">
            <strong>Load More Clicks:</strong> <span id="loadMoreCount">0</span>
        </div>
    </div>
    
    <div class="section">
        <h2>🎯 Button Selector Test</h2>
        <p>Use the button selector to choose any of these buttons:</p>
        
        <button class="button test-btn" onclick="testClick('Button 1')">Test Button 1</button>
        <button class="button test-btn" onclick="testClick('Button 2')">Test Button 2</button>
        <button class="button test-btn" onclick="testClick('Button 3')">Test Button 3</button>
        <input type="button" class="button test-btn" value="Input Button" onclick="testClick('Input Button')">
        
        <div class="stats">
            <strong>Button Clicks:</strong> <span id="buttonClickCount">0</span>
        </div>
    </div>
    
    <div class="section">
        <h2>✅ Checkbox Test</h2>
        <p>Test the "Select All" functionality:</p>
        
        <div class="checkbox-group">
            <label><input type="checkbox"> Option 1</label>
            <label><input type="checkbox"> Option 2</label>
            <label><input type="checkbox"> Option 3</label>
            <label><input type="checkbox"> Option 4</label>
            <label><input type="checkbox"> Option 5</label>
        </div>
    </div>
    
    <div class="section">
        <h2>⬇️ Download Test</h2>
        <p>Test download button detection:</p>
        
        <button class="button download-btn" onclick="download()">Download</button>
        <a href="#" class="button download-btn" onclick="download(); return false;">Download Link</a>
        
        <div class="stats">
            <strong>Downloads:</strong> <span id="downloadCount">0</span>
        </div>
    </div>
    
    <div class="section">
        <h2>🔄 Dynamic Content Loading Test</h2>
        <p>Test dynamic content loading simulation:</p>

        <div id="dynamicContent">
            <div class="content-block">Initial Content Block 1</div>
            <div class="content-block">Initial Content Block 2</div>
        </div>

        <button class="button load-more" onclick="loadDynamicContent()">Load More Content</button>

        <div class="stats">
            <strong>Dynamic Content Blocks:</strong> <span id="dynamicContentCount">2</span>
        </div>
    </div>

    <div class="section">
        <h2>🖼️ Image Loading Test</h2>
        <p>Test image force loading (lazy images):</p>

        <img data-src="https://via.placeholder.com/300x200/4CAF50/white?text=Lazy+Image+1" alt="Lazy Image 1" style="width: 300px; height: 200px; background: #ddd; margin: 10px;">
        <img data-src="https://via.placeholder.com/300x200/2196F3/white?text=Lazy+Image+2" alt="Lazy Image 2" style="width: 300px; height: 200px; background: #ddd; margin: 10px;">
        <img data-original="https://via.placeholder.com/300x200/FF9800/white?text=Lazy+Image+3" alt="Lazy Image 3" style="width: 300px; height: 200px; background: #ddd; margin: 10px;">
        <img data-lazy-src="https://via.placeholder.com/300x200/9C27B0/white?text=Lazy+Image+4" alt="Lazy Image 4" style="width: 300px; height: 200px; background: #ddd; margin: 10px;">
    </div>
    
    <div class="section">
        <h2>📊 Extension Status</h2>
        <p>Check the extension status:</p>
        <ul>
            <li>Look for the blue indicator in the top-right corner</li>
            <li>Open the extension popup to see live statistics</li>
            <li>Check browser console (F12) for detailed logs</li>
            <li>Test all features systematically</li>
        </ul>
    </div>
    
    <div class="long-content">
        Long scrollable content for testing<br>
        Scroll down to see more content!
    </div>
    
    <div class="section">
        <h2>🎉 Test Complete</h2>
        <p>If you can see this, scrolling worked! The extension should have:</p>
        <ul>
            <li>✅ Loaded successfully without errors</li>
            <li>✅ Shown a blue indicator in the top-right</li>
            <li>✅ Responded to popup controls</li>
            <li>✅ Detected and clicked buttons correctly</li>
        </ul>
    </div>
    
    <script>
        let loadMoreCount = 0;
        let buttonClickCount = 0;
        let downloadCount = 0;
        let dynamicContentCount = 2;

        function loadMore() {
            loadMoreCount++;
            document.getElementById('loadMoreCount').textContent = loadMoreCount;
            console.log('🔴 Load More clicked!', loadMoreCount);
        }

        function testClick(buttonName) {
            buttonClickCount++;
            document.getElementById('buttonClickCount').textContent = buttonClickCount;
            console.log('🎯 Button clicked:', buttonName, buttonClickCount);
        }

        function download() {
            downloadCount++;
            document.getElementById('downloadCount').textContent = downloadCount;
            console.log('⬇️ Download clicked!', downloadCount);
        }

        function loadDynamicContent() {
            const container = document.getElementById('dynamicContent');
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'content-block';
            loadingDiv.style.background = '#f0f0f0';
            loadingDiv.textContent = 'Loading...';
            container.appendChild(loadingDiv);

            // Simulate network delay
            setTimeout(() => {
                for (let i = 0; i < 3; i++) {
                    dynamicContentCount++;
                    const newContent = document.createElement('div');
                    newContent.className = 'content-block';
                    newContent.textContent = `Dynamic Content Block ${dynamicContentCount}`;
                    newContent.style.background = `hsl(${Math.random() * 360}, 70%, 90%)`;
                    container.appendChild(newContent);
                }

                // Remove loading indicator
                container.removeChild(loadingDiv);

                // Update counter
                document.getElementById('dynamicContentCount').textContent = dynamicContentCount;

                console.log('🔄 Dynamic content loaded! Total blocks:', dynamicContentCount);
            }, 1000 + Math.random() * 2000); // Random delay 1-3 seconds
        }

        // Simulate lazy image loading with multiple data attributes
        setTimeout(() => {
            document.querySelectorAll('img[data-src], img[data-original], img[data-lazy-src]').forEach(img => {
                if (!img.src) {
                    const src = img.dataset.src || img.dataset.original || img.dataset.lazySrc;
                    if (src) {
                        img.src = src;
                        console.log('🖼️ Lazy image loaded:', img.alt);
                    }
                }
            });
        }, 2000);

        // Add scroll event listener for testing
        let lastScrollTime = 0;
        window.addEventListener('scroll', () => {
            const now = Date.now();
            if (now - lastScrollTime > 100) { // Throttle to every 100ms
                lastScrollTime = now;
                console.log('📜 Page scrolled to:', window.pageYOffset);
            }
        });

        // Add mutation observer to detect when extension modifies the page
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    console.log('🔍 DOM mutation detected:', mutation.addedNodes.length, 'nodes added');
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('🚀 AutoScroller Pro Test Page loaded - Extension should be visible in top-right corner');
        console.log('📊 Test page features:');
        console.log('  - Multiple Load More button variations (English & German)');
        console.log('  - Dynamic content loading simulation');
        console.log('  - Lazy image loading with different data attributes');
        console.log('  - Scroll event monitoring');
        console.log('  - DOM mutation detection');
    </script>
</body>
</html>